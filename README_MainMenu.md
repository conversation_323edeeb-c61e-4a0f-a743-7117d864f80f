# Main Menu pre Godot

Tento GDScript vytvorí perfektné main menu pre tvoju Godot hru používajúc assety z priečinka `assets/`.

## Súbory
- `MainMenu.gd` - <PERSON><PERSON><PERSON><PERSON> script
- `MainMenu.tscn` - Scene súbor

## Funkcie

### ✨ Vizuálne efekty
- **Animované pozadie** - Používa Scalable_1.png
- **Štýlové tlačidlá** - Automaticky mení textúry pri hover/click
- **Fade prechody** - Plynulé prechody medzi scénami
- **Animácia vstupu** - Menu sa animovane zobrazí
- **Hover efekty** - Tlačidlá sa zväčšia pri hover

### 🎮 Ovládanie
- **Myš** - Klikanie na tlačidlá
- **Klávesnica** - Tab na navigáciu, Enter/Space na potvrdenie
- **Escape** - Ukončenie hry

### 🔊 Audio podpora
- Hover zvuky pre tlačidlá
- Click zvuky
- Background hudba

### 📱 Responzívny dizajn
- <PERSON>k<PERSON> prispôsobenie veľkosti okna
- Centrované elementy

## Inštalácia

1. **Skopíruj súbory** do root priečinka tvojho Godot projektu
2. **Uisti sa**, že máš assety v správnej štruktúre:
   ```
   res://assets/
   ├── Buttons/
   │   ├── Buttons_1.png
   │   ├── Buttons_3.png
   │   ├── Buttons_4.png
   │   └── Button_Selected.png
   ├── Scalable screen/
   │   ├── Scalable_1.png
   │   ├── Scalable_2.png
   │   └── Title_Holder.png
   └── Fade effects/
       └── Fade_1.png
   ```

3. **Otvor MainMenu.tscn** v Godot editore
4. **Nastav ako hlavnú scénu** (Project Settings > Main Scene)

## Prispôsobenie

### Zmena textu
V `MainMenu.gd` nájdi:
```gdscript
title_label.text = "TVOJA HRA"  # Zmeň názov hry
```

### Pridanie nových tlačidiel
```gdscript
var new_button = create_menu_button("NOVÉ TLAČIDLO", "new_action")
menu_container.add_child(new_button)
```

### Pripojenie scén
Zmeň tieto riadky v `MainMenu.gd`:
```gdscript
func _on_play_game_requested():
    get_tree().change_scene_to_file("res://scenes/Game.tscn")

func _on_settings_requested():
    get_tree().change_scene_to_file("res://scenes/Settings.tscn")
```

### Pridanie audio súborov
```gdscript
func setup_audio():
    button_hover_sound.stream = preload("res://audio/hover.ogg")
    button_click_sound.stream = preload("res://audio/click.ogg")
    background_music.stream = preload("res://audio/menu_music.ogg")
```

## Signály

Menu emituje tieto signály:
- `play_game_requested` - Keď hráč klikne na "HRAŤ"
- `settings_requested` - Keď hráč klikne na "NASTAVENIA"  
- `credits_requested` - Keď hráč klikne na "KREDITY"
- `quit_requested` - Keď hráč klikne na "UKONČIŤ"

## Tipy

1. **Pre lepší výkon** - Preload len potrebné textúry
2. **Pre mobilné zariadenia** - Zväčši tlačidlá (custom_minimum_size)
3. **Pre gamepad podporu** - Pridaj InputMap akcie
4. **Pre lokalizáciu** - Použij TranslationServer

## Riešenie problémov

**Chyba: "Invalid get index 'texture'"**
- Skontroluj, či sú všetky PNG súbory správne importované v Godot

**Tlačidlá sa nezobrazujú**
- Uisti sa, že cesty k assetom sú správne
- Skontroluj, či sú súbory v res://assets/ priečinku

**Animácie nefungujú**
- Skontroluj, či je Tween správne vytvorený
- Uisti sa, že _ready() funkcia sa volá

Enjoy your perfect main menu! 🎮
