extends Control

# Main Menu Script pre Godot
# Používa assety z priečinka assets/

# Preload assetov
const BUTTON_NORMAL = preload("res://assets/Buttons/Buttons_1.png")
const BUTTON_HOVER = preload("res://assets/Buttons/Buttons_3.png")
const BUTTON_PRESSED = preload("res://assets/Buttons/Buttons_4.png")
const BUTTON_SELECTED = preload("res://assets/Buttons/Button_Selected.png")

const BACKGROUND_MAIN = preload("res://assets/Scalable screen/Scalable_1.png")
const BACKGROUND_PANEL = preload("res://assets/Scalable screen/Scalable_2.png")
const TITLE_HOLDER = preload("res://assets/Scalable screen/Title_Holder.png")

const FADE_EFFECT = preload("res://assets/Fade effects/Fade_1.png")

# UI Nodes
@onready var background: NinePatchRect
@onready var title_panel: NinePatchRect
@onready var title_label: Label
@onready var menu_container: VBoxContainer
@onready var fade_overlay: ColorRect

# Menu tlačidlá
@onready var play_button: Button
@onready var settings_button: Button
@onready var credits_button: Button
@onready var quit_button: Button

# Audio
@onready var button_hover_sound: AudioStreamPlayer
@onready var button_click_sound: AudioStreamPlayer
@onready var background_music: AudioStreamPlayer

# Animácie
@onready var animation_player: AnimationPlayer
@onready var tween: Tween

# Signály
signal play_game_requested
signal settings_requested
signal credits_requested
signal quit_requested

func _ready():
	setup_ui()
	setup_buttons()
	setup_animations()
	setup_audio()
	animate_menu_entrance()

func setup_ui():
	# Vytvorenie hlavného pozadia
	background = NinePatchRect.new()
	background.texture = BACKGROUND_MAIN
	background.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
	add_child(background)
	
	# Fade overlay pre prechody
	fade_overlay = ColorRect.new()
	fade_overlay.color = Color.BLACK
	fade_overlay.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
	fade_overlay.modulate.a = 0.0
	add_child(fade_overlay)
	
	# Title panel
	title_panel = NinePatchRect.new()
	title_panel.texture = TITLE_HOLDER
	title_panel.size = Vector2(400, 100)
	title_panel.position = Vector2(
		(get_viewport().get_visible_rect().size.x - title_panel.size.x) / 2,
		50
	)
	add_child(title_panel)
	
	# Title text
	title_label = Label.new()
	title_label.text = "TVOJA HRA"
	title_label.add_theme_font_size_override("font_size", 32)
	title_label.add_theme_color_override("font_color", Color.WHITE)
	title_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	title_label.vertical_alignment = VERTICAL_ALIGNMENT_CENTER
	title_label.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
	title_panel.add_child(title_label)
	
	# Menu container
	menu_container = VBoxContainer.new()
	menu_container.size = Vector2(300, 400)
	menu_container.position = Vector2(
		(get_viewport().get_visible_rect().size.x - menu_container.size.x) / 2,
		200
	)
	menu_container.add_theme_constant_override("separation", 20)
	add_child(menu_container)

func setup_buttons():
	# Vytvorenie tlačidiel
	play_button = create_menu_button("HRAŤ", "play")
	settings_button = create_menu_button("NASTAVENIA", "settings")
	credits_button = create_menu_button("KREDITY", "credits")
	quit_button = create_menu_button("UKONČIŤ", "quit")
	
	# Pridanie do kontajnera
	menu_container.add_child(play_button)
	menu_container.add_child(settings_button)
	menu_container.add_child(credits_button)
	menu_container.add_child(quit_button)

func create_menu_button(text: String, action: String) -> Button:
	var button = Button.new()
	button.text = text
	button.custom_minimum_size = Vector2(280, 60)
	
	# Nastavenie štýlu tlačidla
	var style_normal = StyleBoxTexture.new()
	style_normal.texture = BUTTON_NORMAL
	style_normal.set_expand_margin_all(10)
	
	var style_hover = StyleBoxTexture.new()
	style_hover.texture = BUTTON_HOVER
	style_hover.set_expand_margin_all(10)
	
	var style_pressed = StyleBoxTexture.new()
	style_pressed.texture = BUTTON_PRESSED
	style_pressed.set_expand_margin_all(10)
	
	button.add_theme_stylebox_override("normal", style_normal)
	button.add_theme_stylebox_override("hover", style_hover)
	button.add_theme_stylebox_override("pressed", style_pressed)
	button.add_theme_stylebox_override("focus", style_hover)
	
	# Font nastavenia
	button.add_theme_font_size_override("font_size", 18)
	button.add_theme_color_override("font_color", Color.WHITE)
	button.add_theme_color_override("font_hover_color", Color.YELLOW)
	button.add_theme_color_override("font_pressed_color", Color.GRAY)
	
	# Pripojenie signálov
	button.pressed.connect(_on_button_pressed.bind(action))
	button.mouse_entered.connect(_on_button_hover.bind(button))
	button.focus_entered.connect(_on_button_hover.bind(button))
	
	return button

func setup_animations():
	# Animation Player pre komplexné animácie
	animation_player = AnimationPlayer.new()
	add_child(animation_player)
	
	# Tween pre jednoduché animácie
	tween = create_tween()

func setup_audio():
	# Audio pre hover efekt
	button_hover_sound = AudioStreamPlayer.new()
	add_child(button_hover_sound)
	
	# Audio pre klik
	button_click_sound = AudioStreamPlayer.new()
	add_child(button_click_sound)
	
	# Background music
	background_music = AudioStreamPlayer.new()
	background_music.autoplay = true
	background_music.stream_paused = false
	add_child(background_music)

func animate_menu_entrance():
	# Animácia vstupu menu
	title_panel.modulate.a = 0.0
	title_panel.position.y -= 50
	
	for button in menu_container.get_children():
		button.modulate.a = 0.0
		button.position.x += 100
	
	# Animácia title
	var title_tween = create_tween()
	title_tween.parallel().tween_property(title_panel, "modulate:a", 1.0, 0.8)
	title_tween.parallel().tween_property(title_panel, "position:y", title_panel.position.y + 50, 0.8)
	title_tween.tween_delay(0.3)
	
	# Animácia tlačidiel
	for i in range(menu_container.get_child_count()):
		var button = menu_container.get_child(i)
		var button_tween = create_tween()
		button_tween.tween_delay(0.5 + i * 0.1)
		button_tween.parallel().tween_property(button, "modulate:a", 1.0, 0.5)
		button_tween.parallel().tween_property(button, "position:x", button.position.x - 100, 0.5)

func _on_button_hover(button: Button):
	# Hover efekt
	if button_hover_sound and button_hover_sound.stream:
		button_hover_sound.play()
	
	# Animácia hover
	var hover_tween = create_tween()
	hover_tween.tween_property(button, "scale", Vector2(1.05, 1.05), 0.1)

func _on_button_pressed(action: String):
	# Click sound
	if button_click_sound and button_click_sound.stream:
		button_click_sound.play()
	
	# Fade out animácia
	var fade_tween = create_tween()
	fade_tween.tween_property(fade_overlay, "modulate:a", 1.0, 0.5)
	
	# Čakanie na dokončenie animácie
	await fade_tween.finished
	
	# Spustenie akcie
	match action:
		"play":
			play_game_requested.emit()
		"settings":
			settings_requested.emit()
		"credits":
			credits_requested.emit()
		"quit":
			quit_requested.emit()

func fade_in():
	# Fade in animácia pri návrate do menu
	var fade_tween = create_tween()
	fade_tween.tween_property(fade_overlay, "modulate:a", 0.0, 0.5)

func _on_play_game_requested():
	# Implementácia spustenia hry
	print("Spúšťa sa hra...")
	# get_tree().change_scene_to_file("res://scenes/Game.tscn")

func _on_settings_requested():
	# Implementácia nastavení
	print("Otváranie nastavení...")
	# get_tree().change_scene_to_file("res://scenes/Settings.tscn")

func _on_credits_requested():
	# Implementácia kreditov
	print("Zobrazenie kreditov...")
	# get_tree().change_scene_to_file("res://scenes/Credits.tscn")

func _on_quit_requested():
	# Implementácia ukončenia
	print("Ukončovanie hry...")
	get_tree().quit()

# Keyboard navigation
func _input(event):
	if event is InputEventKey and event.pressed:
		match event.keycode:
			KEY_ESCAPE:
				_on_button_pressed("quit")
			KEY_ENTER, KEY_SPACE:
				if get_viewport().gui_get_focus_owner() is Button:
					get_viewport().gui_get_focus_owner().pressed.emit()

func _notification(what):
	if what == NOTIFICATION_RESIZED:
		_on_viewport_size_changed()

func _on_viewport_size_changed():
	# Prispôsobenie veľkosti viewportu
	if title_panel:
		title_panel.position.x = (get_viewport().get_visible_rect().size.x - title_panel.size.x) / 2
	if menu_container:
		menu_container.position.x = (get_viewport().get_visible_rect().size.x - menu_container.size.x) / 2
